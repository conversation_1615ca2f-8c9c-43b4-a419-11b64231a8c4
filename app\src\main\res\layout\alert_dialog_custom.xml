<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/bg_rounded_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="450dp">

    <TextView
        android:id="@+id/tv_title"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="240dp"
        android:layout_marginTop="12dp"
        android:fillViewport="true">

        <TextView
            android:id="@+id/tv_message"
            android:textSize="16sp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </ScrollView>

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:gravity="end">

            <Button
                android:id="@+id/btn_negative"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:textSize="16sp"/>

            <View
                android:id="@+id/btn_spacing_neg_neu"
                android:layout_width="12dp"
                android:layout_height="0dp" />

            <Button
                android:id="@+id/btn_neutral"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:textSize="16sp"
                android:visibility="gone" />

            <View
                android:id="@+id/btn_spacing_neu_pos"
                android:layout_width="12dp"
                android:layout_height="0dp" />

            <Button
                android:id="@+id/btn_positive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:textSize="16sp" />
        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>