name: Android CI

on:
  push:
    branches:
      - "**"
    tags:
      - v*
  pull_request:
    branches:
      - "**"
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: "recursive"

      - name: Setup Java JDK
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: temurin

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Accept Licenses
        run: yes | sdkmanager --licenses

      - name: Install Build Tools 35.0.0
        run: sdkmanager "build-tools;35.0.0"

      - name: Verify installed SDK Packages
        run: sdkmanager --list

      - name: Setup Xmake
        uses: xmake-io/github-action-setup-xmake@v1
        with:
          xmake-version: "2.9.9"
          actions-cache-folder: ".xmake-cache"
          actions-cache-key: "ubuntulinux-ci"

      - name: Setup Android NDK
        uses: nttld/setup-ndk@v1
        with:
          ndk-version: r25c
          link-to-sdk: true

      - name: Grant Execute Permission to gradlew
        run: chmod +x ./gradlew

      - name: Build Debug and Unsigned Release APKs
        run: ./gradlew assembleDebug assembleRelease

      - name: Upload Debug APK
        uses: actions/upload-artifact@v4.6.2
        with:
          name: LeviLauncher-debug-apk
          path: app/build/outputs/apk/debug/*.apk

      - name: Upload Unsigned Release APK
        uses: actions/upload-artifact@v4.6.2
        with:
          name: LeviLauncher-release-unsigned-apk
          path: app/build/outputs/apk/release/*-unsigned.apk

      - name: Sign Release APK
        if: startsWith(github.ref, 'refs/tags/')
        uses: kevin-david/zipalign-sign-android-release@v2
        id: sign_app
        with:
          releaseDirectory: app/build/outputs/apk/release
          signingKeyBase64: ${{ secrets.SIGNING_KEY_STORE }}
          alias: ${{ secrets.SIGNING_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.SIGNING_STORE_PASSWORD }}
          keyPassword: ${{ secrets.SIGNING_KEY_PASSWORD }}
          zipAlign: true
        env:
          BUILD_TOOLS_VERSION: "35.0.0"

      - name: Rename APK with tag
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          TAG=${GITHUB_REF_NAME}
          SIGNED_APK="${{ steps.sign_app.outputs.signedReleaseFile }}"
          APK_DST="LeviLauncher-${TAG}-release.apk"
          cp "$SIGNED_APK" "$APK_DST"
          echo "Signed APK renamed to $APK_DST"

      - name: Upload Signed APK Artifact
        if: startsWith(github.ref, 'refs/tags/')
        uses: actions/upload-artifact@v4.6.2
        with:
          name: LeviLauncher-${{ github.ref_name }}-release.apk
          path: LeviLauncher-${{ github.ref_name }}-release.apk

      - name: Release APK to GitHub Releases
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v2
        with:
          files: LeviLauncher-${{ github.ref_name }}-release.apk
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
